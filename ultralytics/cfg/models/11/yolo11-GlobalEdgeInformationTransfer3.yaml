# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLO11 object detection model with P3-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolo11n.yaml' will call yolo11.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.50, 0.25, 1024] # summary: 319 layers, 2624080 parameters, 2624064 gradients, 6.6 GFLOPs
  s: [0.50, 0.50, 1024] # summary: 319 layers, 9458752 parameters, 9458736 gradients, 21.7 GFLOPs
  m: [0.50, 1.00, 512] # summary: 409 layers, 20114688 parameters, 20114672 gradients, 68.5 GFLOPs
  l: [1.00, 1.00, 512] # summary: 631 layers, 25372160 parameters, 25372144 gradients, 87.6 GFLOPs
  x: [1.00, 1.50, 512] # summary: 631 layers, 56966176 parameters, 56966160 gradients, 196.0 GFLOPs

# YOLO11n backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 2, C3k2, [256, False, 0.25]]

  - [-1, 1, MutilScaleEdgeInfoGenetator, [[128, 256, 512]]] # 3
  - [3, 1, GetIndexOutput, [0]] # 4-P3/8
  - [3, 1, GetIndexOutput, [1]] # 5-P4/16
  - [3, 1, GetIndexOutput, [2]] # 6-P5/32

  - [2, 1, Conv, [256, 3, 2]] # 7-P3/8
  - [-1, 2, C3k2, [512, False, 0.25]]
  - [[4, -1], 1, ConvEdgeFusion, [512]] # 9-P3/8
  - [-1, 1, Conv, [512, 3, 2]] # 10-P4/16
  - [-1, 2, C3k2, [512, True]]
  - [[5, -1], 1, ConvEdgeFusion, [512]] # 12-P4/16
  - [-1, 1, Conv, [1024, 3, 2]] # 13-P5/32
  - [-1, 2, C3k2, [1024, True]]
  - [[6, -1], 1, ConvEdgeFusion, [1024]] # 15-P5/32
  - [-1, 1, SPPF, [1024, 5]] # 16
  - [-1, 2, C2PSA, [1024]] # 17

# YOLO11n head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 12], 1, Concat, [1]] # cat backbone P4
  - [-1, 2, C3k2, [512, False]] # 20

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 9], 1, Concat, [1]] # cat backbone P3
  - [-1, 2, C3k2, [256, False]] # 23 (P3/8-small)
  - [[4, -1], 1, ConvEdgeFusion, [512]] # 24-P3/8

  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 20], 1, Concat, [1]] # cat head P4
  - [-1, 2, C3k2, [512, False]] # 27 (P4/16-medium)
  - [[5, -1], 1, ConvEdgeFusion, [512]] # 28-P4/16

  - [-1, 1, Conv, [512, 3, 2]]
  - [[-1, 17], 1, Concat, [1]] # cat head P5
  - [-1, 2, C3k2, [1024, True]] # 31 (P5/32-large)
  - [[6, -1], 1, ConvEdgeFusion, [1024]] # 32-P5/32

  - [[24, 28, 32], 1, Detect, [nc]] # Detect(P3, P4, P5)
