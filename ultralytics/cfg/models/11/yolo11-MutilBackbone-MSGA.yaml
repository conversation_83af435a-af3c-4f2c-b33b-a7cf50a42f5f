# Ultralytics YOLO 🚀, AGPL-3.0 license
# RT-DETR-l object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/rtdetr

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-cls.yaml' will call yolov8-cls.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024] # YOLOv8n summary: 225 layers,  3157200 parameters,  3157184 gradients,   8.9 GFLOPs
  s: [0.33, 0.50, 1024] # YOLOv8s summary: 225 layers, 11166560 parameters, 11166544 gradients,  28.8 GFLOPs
  m: [0.67, 0.75, 768] # YOLOv8m summary: 295 layers, 25902640 parameters, 25902624 gradients,  79.3 GFLOPs
  l: [1.00, 1.00, 512] # YOLOv8l summary: 365 layers, 43691520 parameters, 43691504 gradients, 165.7 GFLOPs
  x: [1.00, 1.25, 512] # YOLOv8x summary: 365 layers, 68229648 parameters, 68229632 gradients, 258.5 GFLOPs

# From BiliBili 魔鬼面具
backbone:
  - [-1, 1, HGStem, [16, 32]]  # 0-P2/4

  - [0, 2, C3k2, [32, False, 0.25]] # 1-P2/4
  - [-1, 1, Conv, [64, 3, 2]] # 2-P3/8
  - [-1, 2, C3k2, [64, False, 0.25]] # 3-P3/8

  - [0, 3, HGBlock, [16, 32, 3]] # 4-P2/4
  - [-1, 1, DWConv, [64, 3, 2, 1, False]]  # 5-P3/8
  - [-1, 3, HGBlock, [32, 64, 3]]   # 6-P3/8

  - [[3, 6], 1, MultiScaleGatedAttn, []] # 7-P3/8

  - [7, 1, Conv, [128, 3, 2]]  # 8-P4/16
  - [-1, 2, C3k2, [128, True]] # 9-P4/16

  - [7, 1, DWConv, [128, 3, 2, 1, False]]  # 10-P4/16
  - [-1, 3, HGBlock, [64, 128, 5, True, False]]  # cm, c2, k, light, shortcut
  - [-1, 3, HGBlock, [64, 128, 5, True, True]]
  - [-1, 3, HGBlock, [64, 128, 5, True, True]]  # 13-P4/16

  - [[9, 13], 1, MultiScaleGatedAttn, []] # 14-P4/16

  - [14, 1, Conv, [256, 3, 2]]  # 15-P5/32
  - [-1, 2, C3k2, [256, True]] # 16-P5/32

  - [14, 1, DWConv, [256, 3, 2, 1, False]]  # 17-P5/32
  - [-1, 3, HGBlock, [128, 256, 5, True, False]]  # 18-P5/32

  - [[16, 18], 1, MultiScaleGatedAttn, []] # 19-P5/32
  - [-1, 1, SPPF, [1024, 5]] # 20-P5/32
  - [-1, 1, C2PSA, [1024]] # 21-P5/32

# YOLOv8.0n head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 14], 1, Concat, [1]] # cat backbone P4
  - [-1, 2, C3k2, [512, False]] # 24

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 7], 1, Concat, [1]] # cat backbone P3
  - [-1, 2, C3k2, [256, False]] # 27 (P3/8-small)

  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 24], 1, Concat, [1]] # cat head P4
  - [-1, 2, C3k2, [512, False]] # 30 (P4/16-medium)

  - [-1, 1, Conv, [512, 3, 2]]
  - [[-1, 21], 1, Concat, [1]] # cat head P5
  - [-1, 2, C3k2, [1024, True]] # 33 (P5/32-large)

  - [[27, 30, 33], 1, Detect, [nc]] # Detect(P3, P4, P5)