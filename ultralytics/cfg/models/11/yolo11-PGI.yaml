# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLO11 object detection model with P3-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolo11n.yaml' will call yolo11.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.50, 0.25, 1024] # summary: 319 layers, 2624080 parameters, 2624064 gradients, 6.6 GFLOPs
  s: [0.50, 0.50, 1024] # summary: 319 layers, 9458752 parameters, 9458736 gradients, 21.7 GFLOPs
  m: [0.50, 1.00, 512] # summary: 409 layers, 20114688 parameters, 20114672 gradients, 68.5 GFLOPs
  l: [1.00, 1.00, 512] # summary: 631 layers, 25372160 parameters, 25372144 gradients, 87.6 GFLOPs
  x: [1.00, 1.50, 512] # summary: 631 layers, 56966176 parameters, 56966160 gradients, 196.0 GFLOPs

# YOLO11n backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Silence, []] # 0
  - [-1, 1, Conv, [64, 3, 2]] # 1-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 2-P2/4
  - [-1, 2, C3k2, [256, False, 0.25]]
  - [-1, 1, Conv, [256, 3, 2]] # 4-P3/8
  - [-1, 2, C3k2, [512, False, 0.25]]
  - [-1, 1, Conv, [512, 3, 2]] # 6-P4/16
  - [-1, 2, C3k2, [512, True]]
  - [-1, 1, Conv, [1024, 3, 2]] # 8-P5/32
  - [-1, 2, C3k2, [1024, True]]
  - [-1, 1, SPPF, [1024, 5]] # 10
  - [-1, 2, C2PSA, [1024]] # 11

# YOLO11n head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 7], 1, Concat, [1]] # cat backbone P4
  - [-1, 2, C3k2, [512, False]] # 14

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 5], 1, Concat, [1]] # cat backbone P3
  - [-1, 2, C3k2, [256, False]] # 17 (P3/8-small)

  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 14], 1, Concat, [1]] # cat head P4
  - [-1, 2, C3k2, [512, False]] # 20 (P4/16-medium)

  - [-1, 1, Conv, [512, 3, 2]]
  - [[-1, 11], 1, Concat, [1]] # cat head P5
  - [-1, 2, C3k2, [1024, True]] # 23 (P5/32-large)

  # routing
  - [5, 1, CBLinear, [[256]]] # 24
  - [7, 1, CBLinear, [[256, 512]]] # 25
  - [9, 1, CBLinear, [[256, 512, 1024]]] # 26

  # conv down
  - [0, 1, Conv, [64, 3, 2]]  # 27-P1/2
  # conv down
  - [-1, 1, Conv, [128, 3, 2]]  # 28-P2/4
  - [-1, 2, C3k2, [128, False]] # 29-P2/4

  # conv down fuse
  - [-1, 1, Conv, [256, 3, 2]]  # 30-P3/8
  - [[24, 25, 26, -1], 1, CBFuse, [[0, 0, 0]]] # 31  
  - [-1, 2, C3k2, [256, False]] # 32

  # conv down fuse
  - [-1, 1, Conv, [512, 3, 2]]  # 33-P4/16
  - [[25, 26, -1], 1, CBFuse, [[1, 1]]] # 34
  - [-1, 2, C3k2, [512, False]] # 35

  # conv down fuse
  - [-1, 1, Conv, [1024, 3, 2]]  # 36-P5/32
  - [[26, -1], 1, CBFuse, [[2]]] # 37
  - [-1, 2, C3k2, [1024, True]] # 38

  - [[17, 20, 23, 32, 35, 38], 1, DetectAux, [nc]]  # Detect(P3, P4, P5)
