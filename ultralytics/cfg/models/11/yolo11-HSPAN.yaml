# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLO11 object detection model with P3-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolo11n.yaml' will call yolo11.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.50, 0.25, 1024] # summary: 319 layers, 2624080 parameters, 2624064 gradients, 6.6 GFLOPs
  s: [0.50, 0.50, 1024] # summary: 319 layers, 9458752 parameters, 9458736 gradients, 21.7 GFLOPs
  m: [0.50, 1.00, 512] # summary: 409 layers, 20114688 parameters, 20114672 gradients, 68.5 GFLOPs
  l: [1.00, 1.00, 512] # summary: 631 layers, 25372160 parameters, 25372144 gradients, 87.6 GFLOPs
  x: [1.00, 1.50, 512] # summary: 631 layers, 56966176 parameters, 56966160 gradients, 196.0 GFLOPs

# YOLO11n backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 2, C3k2, [256, False, 0.25]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 2, C3k2, [512, False, 0.25]]
  - [-1, 1, Conv, [512, 3, 2]] # 5-P4/16
  - [-1, 2, C3k2, [512, True]]
  - [-1, 1, Conv, [1024, 3, 2]] # 7-P5/32
  - [-1, 2, C3k2, [1024, True]]
  - [-1, 1, SPPF, [1024, 5]] # 9
  - [-1, 2, C2PSA, [1024]] # 10

# YOLO11n head
head:
  - [-1, 1, ChannelAttention_HSFPN, []] # 11
  - [-1, 1, nn.Conv2d, [256, 1]] # 12
  - [-1, 1, nn.ConvTranspose2d, [256, 3, 2, 1, 1]] # 13

  - [6, 1, ChannelAttention_HSFPN, []] # 14
  - [-1, 1, nn.Conv2d, [256, 1]] # 15
  - [13, 1, ChannelAttention_HSFPN, [4, False]] # 16
  - [[-1, -2], 1, Multiply, []] # 17
  - [[-1, 13], 1, Add, []] # 18
  - [-1, 2, C3k2, [256, False]] # 19 P4/16

  - [13, 1, nn.ConvTranspose2d, [256, 3, 2, 1, 1]] # 20
  - [4, 1, ChannelAttention_HSFPN, []] # 21
  - [-1, 1, nn.Conv2d, [256, 1]] # 22
  - [20, 1, ChannelAttention_HSFPN, [4, False]] # 23
  - [[-1, -2], 1, Multiply, []] # 25
  - [[-1, 20], 1, Add, []] # 25
  - [-1, 2, C3k2, [256, False]] # 26 P3/16

  - [-1, 1, nn.Conv2d, [256, 3, 2, 1]] # 27
  - [19, 1, ChannelAttention_HSFPN, []] # 28
  - [-1, 1, nn.Conv2d, [256, 1]] # 29
  - [27, 1, ChannelAttention_HSFPN, [4, False]] # 30
  - [[-1, -2], 1, Multiply, []] # 31
  - [[-1, 27], 1, Add, []] # 32
  - [-1, 3, C3k2, [256, False]] # 33 P4/16

  - [27, 1, nn.Conv2d, [256, 3, 2, 1]] # 34
  - [12, 1, ChannelAttention_HSFPN, []] # 35
  - [-1, 1, nn.Conv2d, [256, 1]] # 36
  - [34, 1, ChannelAttention_HSFPN, [4, False]] # 37
  - [[-1, -2], 1, Multiply, []] # 38
  - [[-1, 34], 1, Add, []] # 39
  - [-1, 3, C3k2, [256, False]] # 40 P5/32

  - [[26, 33, 40], 1, Detect, [nc]] # Detect(P3, P4, P5)
