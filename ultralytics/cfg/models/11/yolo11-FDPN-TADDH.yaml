# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLO11 object detection model with P3-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolo11n.yaml' will call yolo11.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.50, 0.25, 1024] # summary: 319 layers, 2624080 parameters, 2624064 gradients, 6.6 GFLOPs
  s: [0.50, 0.50, 1024] # summary: 319 layers, 9458752 parameters, 9458736 gradients, 21.7 GFLOPs
  m: [0.50, 1.00, 512] # summary: 409 layers, 20114688 parameters, 20114672 gradients, 68.5 GFLOPs
  l: [1.00, 1.00, 512] # summary: 631 layers, 25372160 parameters, 25372144 gradients, 87.6 GFLOPs
  x: [1.00, 1.50, 512] # summary: 631 layers, 56966176 parameters, 56966160 gradients, 196.0 GFLOPs

# YOLO11n backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
  - [-1, 2, C3k2, [256, False, 0.25]]
  - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
  - [-1, 2, C3k2, [512, False, 0.25]]
  - [-1, 1, Conv, [512, 3, 2]] # 5-P4/16
  - [-1, 2, C3k2, [512, True]]
  - [-1, 1, Conv, [1024, 3, 2]] # 7-P5/32
  - [-1, 2, C3k2, [1024, True]]
  - [-1, 1, SPPF, [1024, 5]] # 9
  - [-1, 2, C2PSA, [1024]] # 10

# YOLO11n head
head:
  - [[10, 6, 4], 1, FocusFeature, []] # 11-P4/16

  - [11, 1, Conv, [256, 3, 2]] # 12-P5/32
  - [[-1, 10], 1, Concat, [1]]  
  - [-1, 2, C3k2, [512, True]]  # 14-P5/32

  - [11, 1, nn.Upsample, [None, 2, 'nearest']] # 15-P3/8
  - [[-1, 4], 1, Concat, [1]]  
  - [-1, 2, C3k2, [256, False]]  # 17-P3/8

  - [[14, 11, 17], 1, FocusFeature, []] # 18-P4/16

  - [18, 1, Conv, [256, 3, 2]] # 19-P5/32
  - [[12, 19, 14], 1, Concat, [1]]  
  - [-1, 2, C3k2, [512, True]]  # 21-P5/32

  - [18, 1, nn.Upsample, [None, 2, 'nearest']] # 22-P3/8
  - [[15, 22, 17], 1, Concat, [1]]  
  - [-1, 2, C3k2, [512, False]]  # 24-P3/8

  - [18, 1, Conv, [512, 1]] # 25

  - [[24, 25, 21], 1, Detect_TADDH, [nc, 512]]  # Detect(P3, P4, P5)