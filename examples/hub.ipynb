{"cells": [{"cell_type": "markdown", "metadata": {"id": "FIzICjaph_Wy"}, "source": ["<a align=\"center\" href=\"https://ultralytics.com/hub\" target=\"_blank\">\n", "<img width=\"1024\", src=\"https://github.com/ultralytics/assets/raw/main/im/ultralytics-hub.png\"></a>\n", "\n", "<div align=\"center\">\n", "\n", "[中文](https://docs.ultralytics.com/zh/hub/) | [한국어](https://docs.ultralytics.com/ko/hub/) | [日本語](https://docs.ultralytics.com/ja/hub/) | [Русский](https://docs.ultralytics.com/ru/hub/) | [<PERSON><PERSON><PERSON>](https://docs.ultralytics.com/de/hub/) | [Français](https://docs.ultralytics.com/fr/hub/) | [Español](https://docs.ultralytics.com/es/hub/) | [Português](https://docs.ultralytics.com/pt/hub/) | [Türkçe](https://docs.ultralytics.com/tr/hub/) | [Tiếng Việt](https://docs.ultralytics.com/vi/hub/) | [العربية](https://docs.ultralytics.com/ar/hub/)\n", "\n", "  <a href=\"https://github.com/ultralytics/hub/actions/workflows/ci.yaml\"><img src=\"https://github.com/ultralytics/hub/actions/workflows/ci.yaml/badge.svg\" alt=\"CI CPU\"></a>\n", "  <a href=\"https://colab.research.google.com/github/ultralytics/hub/blob/main/hub.ipynb\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"></a>\n", "\n", "  <a href=\"https://ultralytics.com/discord\"><img alt=\"Discord\" src=\"https://img.shields.io/discord/1089800235347353640?logo=discord&logoColor=white&label=Discord&color=blue\"></a>\n", "  <a href=\"https://community.ultralytics.com\"><img alt=\"Ultralytics Forums\" src=\"https://img.shields.io/discourse/users?server=https%3A%2F%2Fcommunity.ultralytics.com&logo=discourse&label=Forums&color=blue\"></a>\n", "  <a href=\"https://reddit.com/r/ultralytics\"><img alt=\"Ultralytics Reddit\" src=\"https://img.shields.io/reddit/subreddit-subscribers/ultralytics?style=flat&logo=reddit&logoColor=white&label=Reddit&color=blue\"></a>\n", "\n", "Welcome to the [Ultralytics](https://ultralytics.com/) HUB notebook!\n", "\n", "This notebook allows you to train Ultralytics [YOLO](https://github.com/ultralytics/ultralytics) 🚀 models using [HUB](https://hub.ultralytics.com/). Please browse the HUB <a href=\"https://docs.ultralytics.com/hub/\">Docs</a> for details, raise an issue on <a href=\"https://github.com/ultralytics/hub/issues/new/choose\">GitHub</a> for support, and join our <a href=\"https://ultralytics.com/discord\">Discord</a> community for questions and discussions!\n", "</div>"]}, {"cell_type": "markdown", "metadata": {"id": "eRQ2ow94MiOv"}, "source": ["# Setup\n", "\n", "Pip install `ultralytics` and [dependencies](https://github.com/ultralytics/ultralytics/blob/main/pyproject.toml) and check software and hardware.\n", "\n", "[![PyPI - Version](https://img.shields.io/pypi/v/ultralytics?logo=pypi&logoColor=white)](https://pypi.org/project/ultralytics/) [![Downloads](https://static.pepy.tech/badge/ultralytics)](https://pepy.tech/project/ultralytics) [![PyPI - Python Version](https://img.shields.io/pypi/pyversions/ultralytics?logo=python&logoColor=gold)](https://pypi.org/project/ultralytics/)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FyDnXd-n4c7Y", "outputId": "e1d713ec-e8a6-4422-fe61-c76ec9f03df5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ultralytics 8.2.3 🚀 Python-3.10.12 torch-2.2.1+cu121 CUDA:0 (T4, 15102MiB)\n", "Setup complete ✅ (2 CPUs, 12.7 GB RAM, 28.8/78.2 GB disk)\n"]}], "source": ["%pip install ultralytics  # install\n", "from ultralytics import YOLO, checks, hub\n", "\n", "checks()  # checks"]}, {"cell_type": "markdown", "metadata": {"id": "cQ9BwaAqxAm4"}, "source": ["# Start\n", "\n", "⚡ Login with your API key, load your YOLO 🚀 model and start training in 3 lines of code!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "XSlZaJ9Iw_iZ"}, "outputs": [], "source": ["# Log in to HUB using your API key (https://hub.ultralytics.com/settings?tab=api+keys)\n", "hub.login(\"YOUR_API_KEY\")\n", "\n", "# Load your model from HUB (replace 'YOUR_MODEL_ID' with your model ID)\n", "model = YOLO(\"https://hub.ultralytics.com/models/YOUR_MODEL_ID\")\n", "\n", "# Train the model\n", "results = model.train()"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Ultralytics HUB", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}